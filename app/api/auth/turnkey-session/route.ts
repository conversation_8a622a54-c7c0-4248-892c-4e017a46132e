import { NextResponse } from "next/server";
import { cookies } from "next/headers";
import { COOKIES_TURNKEY_SESSION_KEY } from "@/constants/common";

export async function GET() {
  try {
    const cookieStore = await cookies();
    const turnkeySessionData = cookieStore.get(COOKIES_TURNKEY_SESSION_KEY)?.value;
    
    if (!turnkeySessionData) {
      return NextResponse.json({ error: "No Turnkey session found" }, { status: 401 });
    }

    const parsedData = JSON.parse(turnkeySessionData);
    return NextResponse.json(parsedData, { status: 200 });
  } catch (e: any) {
    return NextResponse.json(
      { error: e?.message || "Failed to retrieve Turnkey session" },
      { status: 500 }
    );
  }
}

export async function DELETE() {
  try {
    const response = NextResponse.json({ ok: true }, { status: 200 });
    response.cookies.set(COOKIES_TURNKEY_SESSION_KEY, "", {
      httpOnly: true,
      secure: true,
      sameSite: "lax",
      path: "/",
      expires: new Date(0),
    });
    return response;
  } catch (e: any) {
    return NextResponse.json(
      { error: e?.message || "Failed to clear Turnkey session" },
      { status: 500 }
    );
  }
}
