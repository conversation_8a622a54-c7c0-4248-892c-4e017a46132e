"use client";

import React from "react";
import { TurnkeySessionTest } from "@/components/TurnkeySessionTest";
import { TurnkeySessionInfo } from "@/components/TurnkeySessionInfo";

export default function TestTurnkeyPage() {
  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      <h1 className="text-3xl font-bold mb-8">Turnkey Session Testing</h1>
      
      <div className="space-y-6">
        {/* Session Info Component */}
        <div>
          <h2 className="text-xl font-semibold mb-3">Current Session Status</h2>
          <TurnkeySessionInfo />
        </div>
        
        {/* IndexedDB Client Test Component */}
        <div>
          <h2 className="text-xl font-semibold mb-3">IndexedDB Client Testing</h2>
          <TurnkeySessionTest />
        </div>
        
        {/* Usage Instructions */}
        <div className="p-6 bg-gray-50 rounded-lg border">
          <h2 className="text-xl font-semibold mb-3">How to Use</h2>
          <ol className="list-decimal list-inside space-y-2 text-gray-700">
            <li>First, authenticate with Turnkey using Google OAuth</li>
            <li>Navigate to this page (/test-turnkey)</li>
            <li>Check that your session status shows as authenticated</li>
            <li>Use the test buttons to call IndexedDB client methods</li>
            <li>Check both the UI results and browser console for detailed output</li>
          </ol>
          
          <div className="mt-4 p-3 bg-blue-50 rounded border border-blue-200">
            <h3 className="font-medium text-blue-800 mb-2">Code Example:</h3>
            <pre className="text-sm text-blue-700 overflow-auto">
{`const session = await indexedDbClient.getSession();
if (session) {
  const whoami = await indexedDbClient.getWhoami({
    organizationId: session.organizationId,
  });
}`}
            </pre>
          </div>
        </div>
      </div>
    </div>
  );
}
