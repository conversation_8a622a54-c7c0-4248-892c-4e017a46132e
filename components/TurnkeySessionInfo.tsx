"use client";

import React from "react";
import { useTurnkeySession } from "@/hooks/useTurnkeySession";

export const TurnkeySessionInfo: React.FC = () => {
  const { 
    turnkeySessionData, 
    isLoading, 
    isTurnkeyAuthenticated,
    clearTurnkeySession 
  } = useTurnkeySession();

  if (!isTurnkeyAuthenticated) {
    return null;
  }

  if (isLoading) {
    return <div>Loading Turnkey session...</div>;
  }

  if (!turnkeySessionData) {
    return <div>No Turnkey session data available</div>;
  }

  return (
    <div className="p-4 border rounded-lg bg-gray-50">
      <h3 className="text-lg font-semibold mb-2">Turnkey Session Info</h3>
      <div className="space-y-2 text-sm">
        <div>
          <strong>Organization ID:</strong> {turnkeySessionData.subOrganizationId}
        </div>
        <div>
          <strong>User ID:</strong> {turnkeySessionData.userId}
        </div>
        <div>
          <strong>API Key ID:</strong> {turnkeySessionData.apiKeyId}
        </div>
        <div>
          <strong>Credential Bundle:</strong> 
          <span className="font-mono text-xs break-all">
            {turnkeySessionData.credentialBundle.substring(0, 50)}...
          </span>
        </div>
      </div>
      <button
        onClick={clearTurnkeySession}
        className="mt-3 px-3 py-1 bg-red-500 text-white rounded text-sm hover:bg-red-600"
      >
        Clear Session
      </button>
    </div>
  );
};
