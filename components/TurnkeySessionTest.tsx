"use client";

import React, { useState } from "react";
import { useTurnkeySession } from "@/hooks/useTurnkeySession";

interface TurnkeySession {
  organizationId: string;
  userId: string;
  credentialBundle: string;
  // Add other session properties as needed
}

interface WhoamiResponse {
  organizationId: string;
  organizationName: string;
  userId: string;
  username: string;
  userEmail: string;
  // Add other whoami properties as needed
}

export const TurnkeySessionTest: React.FC = () => {
  const { turnkeySessionData, isTurnkeyAuthenticated } = useTurnkeySession();
  const [session, setSession] = useState<TurnkeySession | null>(null);
  const [whoami, setWhoami] = useState<WhoamiResponse | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const getIndexedDbClient = () => {
    // Access the Turnkey IndexedDB client from the global window object
    const turnkeyHook = (window as any).__turnkeyHook;
    if (!turnkeyHook?.indexedDbClient) {
      throw new Error("Turnkey IndexedDB client not found. Make sure Turnkey SDK is properly initialized.");
    }
    return turnkeyHook.indexedDbClient;
  };

  const testGetSession = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const indexedDbClient = getIndexedDbClient();
      const sessionData = await indexedDbClient.getSession();
      
      if (sessionData) {
        setSession(sessionData);
        console.log("✅ Session retrieved:", sessionData);
      } else {
        setSession(null);
        console.log("ℹ️ No session found in IndexedDB");
      }
    } catch (err: any) {
      const errorMessage = err?.message || "Failed to get session";
      setError(errorMessage);
      console.error("❌ Error getting session:", err);
    } finally {
      setLoading(false);
    }
  };

  const testGetWhoami = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const indexedDbClient = getIndexedDbClient();
      
      // First get the session
      const sessionData = await indexedDbClient.getSession();
      if (!sessionData) {
        throw new Error("No session found. Please authenticate first.");
      }

      // Use organization ID from session or from our stored session data
      const organizationId = sessionData.organizationId || turnkeySessionData?.subOrganizationId;
      if (!organizationId) {
        throw new Error("No organization ID found in session data");
      }

      // Call whoami with the organization ID
      const whoamiData = await indexedDbClient.getWhoami({
        organizationId: organizationId,
      });
      
      setWhoami(whoamiData);
      console.log("✅ Whoami retrieved:", whoamiData);
    } catch (err: any) {
      const errorMessage = err?.message || "Failed to get whoami";
      setError(errorMessage);
      console.error("❌ Error getting whoami:", err);
    } finally {
      setLoading(false);
    }
  };

  const testBothMethods = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const indexedDbClient = getIndexedDbClient();
      
      // Get session first
      const sessionData = await indexedDbClient.getSession();
      if (sessionData) {
        setSession(sessionData);
        console.log("✅ Session retrieved:", sessionData);
        
        // Then get whoami using the session's organization ID
        const organizationId = sessionData.organizationId || turnkeySessionData?.subOrganizationId;
        if (organizationId) {
          const whoamiData = await indexedDbClient.getWhoami({
            organizationId: organizationId,
          });
          setWhoami(whoamiData);
          console.log("✅ Whoami retrieved:", whoamiData);
        } else {
          throw new Error("No organization ID found in session");
        }
      } else {
        throw new Error("No session found in IndexedDB");
      }
    } catch (err: any) {
      const errorMessage = err?.message || "Failed to execute test";
      setError(errorMessage);
      console.error("❌ Error in test:", err);
    } finally {
      setLoading(false);
    }
  };

  const clearResults = () => {
    setSession(null);
    setWhoami(null);
    setError(null);
  };

  if (!isTurnkeyAuthenticated) {
    return (
      <div className="p-4 border rounded-lg bg-yellow-50 border-yellow-200">
        <p className="text-yellow-800">
          Please authenticate with Turnkey first to test IndexedDB client methods.
        </p>
      </div>
    );
  }

  return (
    <div className="p-6 border rounded-lg bg-white shadow-sm">
      <h3 className="text-xl font-semibold mb-4">Turnkey IndexedDB Client Test</h3>
      
      {/* Session Data from Cookies */}
      {turnkeySessionData && (
        <div className="mb-4 p-3 bg-blue-50 rounded border border-blue-200">
          <h4 className="font-medium text-blue-800 mb-2">Stored Session Data (from cookies):</h4>
          <div className="text-sm text-blue-700">
            <div>Organization ID: {turnkeySessionData.subOrganizationId}</div>
            <div>User ID: {turnkeySessionData.userId}</div>
          </div>
        </div>
      )}

      {/* Test Buttons */}
      <div className="flex gap-3 mb-4">
        <button
          onClick={testGetSession}
          disabled={loading}
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
        >
          Test getSession()
        </button>
        
        <button
          onClick={testGetWhoami}
          disabled={loading}
          className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 disabled:opacity-50"
        >
          Test getWhoami()
        </button>
        
        <button
          onClick={testBothMethods}
          disabled={loading}
          className="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600 disabled:opacity-50"
        >
          Test Both Methods
        </button>
        
        <button
          onClick={clearResults}
          disabled={loading}
          className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 disabled:opacity-50"
        >
          Clear Results
        </button>
      </div>

      {/* Loading State */}
      {loading && (
        <div className="mb-4 p-3 bg-gray-50 rounded border">
          <div className="flex items-center gap-2">
            <div className="animate-spin h-4 w-4 border-2 border-gray-400 border-t-transparent rounded-full"></div>
            <span>Testing Turnkey IndexedDB client...</span>
          </div>
        </div>
      )}

      {/* Error Display */}
      {error && (
        <div className="mb-4 p-3 bg-red-50 rounded border border-red-200">
          <h4 className="font-medium text-red-800 mb-1">Error:</h4>
          <p className="text-red-700 text-sm">{error}</p>
        </div>
      )}

      {/* Session Results */}
      {session && (
        <div className="mb-4 p-3 bg-green-50 rounded border border-green-200">
          <h4 className="font-medium text-green-800 mb-2">IndexedDB Session Data:</h4>
          <pre className="text-sm text-green-700 overflow-auto">
            {JSON.stringify(session, null, 2)}
          </pre>
        </div>
      )}

      {/* Whoami Results */}
      {whoami && (
        <div className="mb-4 p-3 bg-indigo-50 rounded border border-indigo-200">
          <h4 className="font-medium text-indigo-800 mb-2">Whoami Data:</h4>
          <pre className="text-sm text-indigo-700 overflow-auto">
            {JSON.stringify(whoami, null, 2)}
          </pre>
        </div>
      )}

      {/* Instructions */}
      <div className="mt-6 p-3 bg-gray-50 rounded border">
        <h4 className="font-medium mb-2">Instructions:</h4>
        <ul className="text-sm text-gray-700 space-y-1">
          <li>• <strong>getSession():</strong> Retrieves the current session from IndexedDB</li>
          <li>• <strong>getWhoami():</strong> Gets user information using the organization ID</li>
          <li>• <strong>Test Both:</strong> Runs both methods in sequence (recommended)</li>
          <li>• Check the browser console for detailed logs</li>
        </ul>
      </div>
    </div>
  );
};
