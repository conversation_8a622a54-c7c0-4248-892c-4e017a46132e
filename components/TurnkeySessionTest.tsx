"use client";

import React, { useState } from "react";
import { useTurnkeySession } from "@/hooks/useTurnkeySession";

interface TurnkeySession {
  organizationId: string;
  userId: string;
  credentialBundle: string;
  // Add other session properties as needed
}

interface WhoamiResponse {
  organizationId: string;
  organizationName: string;
  userId: string;
  username: string;
  userEmail: string;
  // Add other whoami properties as needed
}

export const TurnkeySessionTest: React.FC = () => {
  const [session, setSession] = useState<TurnkeySession | null>(null);
  const [whoami, setWhoami] = useState<WhoamiResponse | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const getIndexedDbClient = () => {
    // Access the Turnkey IndexedDB client from the global window object
    const turnkeyHook = (window as any).__turnkeyHook;
    if (!turnkeyHook?.indexedDbClient) {
      throw new Error(
        "Turnkey IndexedDB client not found. Make sure Turnkey SDK is properly initialized."
      );
    }
    return turnkeyHook.indexedDbClient;
  };

  const testGetSession = async () => {
    setLoading(true);
    setError(null);

    try {
      const indexedDbClient = getIndexedDbClient();
      const sessionData = await indexedDbClient.getSession();

      if (sessionData) {
        setSession(sessionData);
        console.log("✅ Session retrieved:", sessionData);
      } else {
        setSession(null);
        console.log("ℹ️ No session found in IndexedDB");
      }
    } catch (err: any) {
      const errorMessage = err?.message || "Failed to get session";
      setError(errorMessage);
      console.error("❌ Error getting session:", err);
    } finally {
      setLoading(false);
    }
  };

  const getOrganizationIdFromCookie = async () => {
    try {
      // Get the organization ID from the raidenx_turnkey_org_id cookie
      const response = await fetch("/api/auth/turnkey-session", {
        cache: "no-store",
      });
      if (response.ok) {
        const sessionData = await response.json();
        return sessionData?.subOrganizationId;
      }
    } catch (err) {
      console.error("Failed to get organization ID from cookie:", err);
    }
    return null;
  };

  const testGetWhoami = async () => {
    setLoading(true);
    setError(null);

    try {
      const indexedDbClient = getIndexedDbClient();

      // First get the session
      const sessionData = await indexedDbClient.getSession();
      if (!sessionData) {
        throw new Error("No session found. Please authenticate first.");
      }

      // Get organization ID from the raidenx_turnkey_org_id cookie
      const organizationId = await getOrganizationIdFromCookie();
      if (!organizationId) {
        throw new Error(
          "No organization ID found in raidenx_turnkey_org_id cookie"
        );
      }

      console.log("🔑 Using organization ID from cookie:", organizationId);

      // Call whoami with the organization ID
      const whoamiData = await indexedDbClient.getWhoami({
        organizationId: organizationId,
      });

      setWhoami(whoamiData);
      console.log("✅ Whoami retrieved:", whoamiData);
    } catch (err: any) {
      const errorMessage = err?.message || "Failed to get whoami";
      setError(errorMessage);
      console.error("❌ Error getting whoami:", err);
    } finally {
      setLoading(false);
    }
  };

  const testBothMethods = async () => {
    setLoading(true);
    setError(null);

    try {
      const indexedDbClient = getIndexedDbClient();

      // Get session first
      const sessionData = await indexedDbClient.getSession();
      if (sessionData) {
        setSession(sessionData);
        console.log("✅ Session retrieved:", sessionData);

        // Get organization ID from the raidenx_turnkey_org_id cookie
        const organizationId = await getOrganizationIdFromCookie();
        if (organizationId) {
          console.log("🔑 Using organization ID from cookie:", organizationId);
          const whoamiData = await indexedDbClient.getWhoami({
            organizationId: organizationId,
          });
          setWhoami(whoamiData);
          console.log("✅ Whoami retrieved:", whoamiData);
        } else {
          throw new Error(
            "No organization ID found in raidenx_turnkey_org_id cookie"
          );
        }
      } else {
        throw new Error("No session found in IndexedDB");
      }
    } catch (err: any) {
      const errorMessage = err?.message || "Failed to execute test";
      setError(errorMessage);
      console.error("❌ Error in test:", err);
    } finally {
      setLoading(false);
    }
  };

  const clearResults = () => {
    setSession(null);
    setWhoami(null);
    setError(null);
  };

  return (
    <div className="rounded-lg border bg-white p-6 shadow-sm">
      <h3 className="mb-4 text-xl font-semibold">
        Turnkey IndexedDB Client Test
      </h3>

      {/* Test Buttons */}
      <div className="mb-4 flex gap-3">
        <button
          onClick={testGetSession}
          disabled={loading}
          className="rounded bg-blue-500 px-4 py-2 text-white hover:bg-blue-600 disabled:opacity-50"
        >
          Test getSession()
        </button>

        <button
          onClick={testGetWhoami}
          disabled={loading}
          className="rounded bg-green-500 px-4 py-2 text-white hover:bg-green-600 disabled:opacity-50"
        >
          Test getWhoami()
        </button>

        <button
          onClick={testBothMethods}
          disabled={loading}
          className="rounded bg-purple-500 px-4 py-2 text-white hover:bg-purple-600 disabled:opacity-50"
        >
          Test Both Methods
        </button>

        <button
          onClick={clearResults}
          disabled={loading}
          className="rounded bg-gray-500 px-4 py-2 text-white hover:bg-gray-600 disabled:opacity-50"
        >
          Clear Results
        </button>
      </div>

      {/* Loading State */}
      {loading && (
        <div className="mb-4 rounded border bg-gray-50 p-3">
          <div className="flex items-center gap-2">
            <div className="h-4 w-4 animate-spin rounded-full border-2 border-gray-400 border-t-transparent"></div>
            <span>Testing Turnkey IndexedDB client...</span>
          </div>
        </div>
      )}

      {/* Error Display */}
      {error && (
        <div className="mb-4 rounded border border-red-200 bg-red-50 p-3">
          <h4 className="mb-1 font-medium text-red-800">Error:</h4>
          <p className="text-sm text-red-700">{error}</p>
        </div>
      )}

      {/* Session Results */}
      {session && (
        <div className="mb-4 rounded border border-green-200 bg-green-50 p-3">
          <h4 className="mb-2 font-medium text-green-800">
            IndexedDB Session Data:
          </h4>
          <pre className="overflow-auto text-sm text-green-700">
            {JSON.stringify(session, null, 2)}
          </pre>
        </div>
      )}

      {/* Whoami Results */}
      {whoami && (
        <div className="mb-4 rounded border border-indigo-200 bg-indigo-50 p-3">
          <h4 className="mb-2 font-medium text-indigo-800">Whoami Data:</h4>
          <pre className="overflow-auto text-sm text-indigo-700">
            {JSON.stringify(whoami, null, 2)}
          </pre>
        </div>
      )}

      {/* Instructions */}
      <div className="mt-6 rounded border bg-gray-50 p-3">
        <h4 className="mb-2 font-medium">Instructions:</h4>
        <ul className="space-y-1 text-sm text-gray-700">
          <li>
            • <strong>getSession():</strong> Retrieves the current session from
            IndexedDB
          </li>
          <li>
            • <strong>getWhoami():</strong> Gets user information using the
            organization ID
          </li>
          <li>
            • <strong>Test Both:</strong> Runs both methods in sequence
            (recommended)
          </li>
          <li>• Check the browser console for detailed logs</li>
        </ul>
      </div>
    </div>
  );
};
