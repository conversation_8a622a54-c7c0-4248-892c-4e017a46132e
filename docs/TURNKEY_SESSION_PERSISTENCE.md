# Turnkey Session Persistence Implementation

This document describes the implementation of secure HTTP-only cookie storage for Turnkey authentication response data to persist user sessions across browser refreshes.

## Overview

The implementation stores the complete `TurnkeyLoginResponse` data in secure HTTP-only cookies, allowing users to remain authenticated with <PERSON>key even after browser refreshes (F5) or navigation away and return.

## Key Components

### 1. Cookie Storage Constants

**File:** `constants/common.ts`
- Added `COOKIES_TURNKEY_SESSION_KEY` constant for the Turnkey session cookie name
- Follows the same naming pattern as the existing JWT token cookie

### 2. Enhanced Authentication Helpers

**File:** `app/api/auth/_helpers.ts`
- `handleTurnkeyLoginSuccess()`: New function specifically for Turnkey authentication
  - Stores JWT token in the existing cookie
  - Stores Turnkey session data (organization ID, user ID, API key ID, credential bundle) in a separate cookie
  - Uses the same security settings as JWT cookies (httpOnly, secure, sameSite)

### 3. API Routes

#### Turnkey Session Management
**File:** `app/api/auth/turnkey-session/route.ts`
- `GET`: Retrieves stored Turnkey session data from cookies
- `DELETE`: Clears Turnkey session cookies (used during logout)

#### Enhanced JWT Session Management
**File:** `app/api/auth/session-jwt/route.ts`
- Updated `DELETE` method to also clear Turnkey session cookies during logout

### 4. Storage Utilities

**File:** `libs/storage.ts`
- `getTurnkeySessionData()`: Fetches Turnkey session data from the API
- `clearTurnkeySessionData()`: Clears Turnkey session cookies

### 5. React Hook

**File:** `hooks/useTurnkeySession.ts`
- Provides easy access to Turnkey session data in React components
- Automatically loads session data when user is authenticated with Turnkey
- Includes loading states and error handling

### 6. Type Definitions

**File:** `types/turnkey.type.ts`
- Added `TurnkeySessionData` type for the stored session data structure

## Security Features

1. **HTTP-Only Cookies**: Session data is stored in HTTP-only cookies, preventing XSS attacks
2. **Secure Flag**: Cookies are marked as secure for HTTPS-only transmission
3. **SameSite Protection**: Cookies use `sameSite: "lax"` to prevent CSRF attacks
4. **Expiration**: Cookies expire after the same duration as JWT tokens (24 hours)
5. **Automatic Cleanup**: Cookies are automatically cleared during logout

## Usage Examples

### Using the Hook in Components

```typescript
import { useTurnkeySession } from "@/hooks/useTurnkeySession";

function MyComponent() {
  const { 
    turnkeySessionData, 
    isLoading, 
    isTurnkeyAuthenticated 
  } = useTurnkeySession();

  if (isTurnkeyAuthenticated && turnkeySessionData) {
    // Access Turnkey session data
    console.log("Organization ID:", turnkeySessionData.subOrganizationId);
    console.log("User ID:", turnkeySessionData.userId);
  }
}
```

### Manual Session Management

```typescript
import Storage from "@/libs/storage";

// Get session data
const sessionData = await Storage.getTurnkeySessionData();

// Clear session data
await Storage.clearTurnkeySessionData();
```

## Testing

### Browser Console Testing

The implementation includes test utilities that can be used in the browser console:

```javascript
// Run all tests
await window.testTurnkeySession.runAllTests();

// Test session persistence
await window.testTurnkeySession.checkSessionPersistence();

// Simulate page refresh
await window.testTurnkeySession.simulatePageRefresh();
```

### Manual Testing Steps

1. **Login with Turnkey**: Complete the Google OAuth flow
2. **Verify Session Storage**: Check that session data is available
3. **Refresh Page (F5)**: Verify user remains authenticated
4. **Navigate Away and Return**: Verify session persists
5. **Logout**: Verify all session data is cleared

## Integration Points

### Authentication Flow

1. User completes Google OAuth
2. `handleCallbackProvider()` exchanges tokens with backend
3. `handleTurnkeyLoginSuccess()` stores both JWT and Turnkey session data
4. User is redirected to finalize page
5. Session data is automatically available for use

### Logout Flow

1. User initiates logout
2. Turnkey client-side session is cleared
3. HTTP-only cookies are cleared via API call
4. Redux state is cleared
5. User is logged out completely

## Compatibility

- ✅ Maintains compatibility with existing JWT/Redux flow
- ✅ Works with existing authentication patterns
- ✅ Follows established security practices
- ✅ Integrates with existing logout mechanisms

## Files Modified/Added

### Modified Files
- `constants/common.ts`
- `app/api/auth/_helpers.ts`
- `app/api/auth/google/callback/route.ts`
- `app/api/auth/session-jwt/route.ts`
- `types/turnkey.type.ts`
- `libs/storage.ts`
- `hooks/index.ts`
- `layouts/Header.tsx`
- `app/provider.tsx`
- `app/auth/finalize/page.tsx`

### Added Files
- `app/api/auth/turnkey-session/route.ts`
- `hooks/useTurnkeySession.ts`
- `components/TurnkeySessionInfo.tsx`
- `utils/turnkey-session-test.ts`
- `docs/TURNKEY_SESSION_PERSISTENCE.md`
