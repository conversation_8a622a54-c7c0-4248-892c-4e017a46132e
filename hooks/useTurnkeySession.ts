"use client";

import { useCallback, useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { RootState } from "@/store";
import Storage from "@/libs/storage";
import { TurnkeySessionData } from "@/types/turnkey.type";

export const useTurnkeySession = () => {
  const [turnkeySessionData, setTurnkeySessionData] = useState<TurnkeySessionData | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const accessToken = useSelector((state: RootState) => state.user.accessToken);
  const loginMethod = Storage.getLoginMethod();

  const loadTurnkeySession = useCallback(async () => {
    if (loginMethod !== "turnkey" || !accessToken) {
      setTurnkeySessionData(null);
      return;
    }

    setIsLoading(true);
    try {
      const sessionData = await Storage.getTurnkeySessionData();
      setTurnkeySessionData(sessionData);
    } catch (error) {
      console.error("Failed to load Turnkey session:", error);
      setTurnkeySessionData(null);
    } finally {
      setIsLoading(false);
    }
  }, [loginMethod, accessToken]);

  const clearTurnkeySession = useCallback(async () => {
    try {
      await Storage.clearTurnkeySessionData();
      setTurnkeySessionData(null);
    } catch (error) {
      console.error("Failed to clear Turnkey session:", error);
    }
  }, []);

  useEffect(() => {
    loadTurnkeySession();
  }, [loadTurnkeySession]);

  return {
    turnkeySessionData,
    isLoading,
    loadTurnkeySession,
    clearTurnkeySession,
    isTurnkeyAuthenticated: !!turnkeySessionData && loginMethod === "turnkey",
  };
};
