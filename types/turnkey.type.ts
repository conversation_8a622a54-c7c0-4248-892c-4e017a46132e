export type TurnkeyLoginType = {
  email?: string;
  oauthProvider: {
    oidcToken: string;
    providerName: string;
  };
  phoneNumber?: string;
  publicKey?: string;
};

export type TurnkeyLoginResponse = {
  subOrganizationId: string;
  userId: string;
  apiKeyId: string;
  credentialBundle: string;
  jwtToken: string;
};

export type TurnkeySessionData = {
  subOrganizationId: string;
  userId: string;
  apiKeyId: string;
  credentialBundle: string;
};
