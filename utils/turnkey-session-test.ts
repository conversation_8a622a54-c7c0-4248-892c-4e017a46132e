/**
 * Utility functions to test Turnkey session persistence
 * These functions can be used in the browser console to verify the implementation
 */

export const testTurnkeySessionPersistence = {
  /**
   * Test if Turnkey session data persists after page refresh
   */
  async checkSessionPersistence() {
    try {
      const response = await fetch("/api/auth/turnkey-session", {
        cache: "no-store",
      });
      
      if (response.ok) {
        const sessionData = await response.json();
        console.log("✅ Turnkey session data found:", sessionData);
        return sessionData;
      } else if (response.status === 401) {
        console.log("ℹ️ No Turnkey session found (user not authenticated with Turnkey)");
        return null;
      } else {
        console.error("❌ Error retrieving session:", response.status);
        return null;
      }
    } catch (error) {
      console.error("❌ Failed to check session persistence:", error);
      return null;
    }
  },

  /**
   * Test clearing Turnkey session data
   */
  async clearSession() {
    try {
      const response = await fetch("/api/auth/turnkey-session", {
        method: "DELETE",
      });
      
      if (response.ok) {
        console.log("✅ Turnkey session cleared successfully");
        return true;
      } else {
        console.error("❌ Failed to clear session:", response.status);
        return false;
      }
    } catch (error) {
      console.error("❌ Error clearing session:", error);
      return false;
    }
  },

  /**
   * Test JWT token persistence
   */
  async checkJWTPersistence() {
    try {
      const response = await fetch("/api/auth/session-jwt", {
        cache: "no-store",
      });
      
      if (response.ok) {
        const data = await response.json();
        console.log("✅ JWT token found:", data.jwtToken ? "Present" : "Missing");
        return !!data.jwtToken;
      } else if (response.status === 401) {
        console.log("ℹ️ No JWT token found (user not authenticated)");
        return false;
      } else {
        console.error("❌ Error retrieving JWT:", response.status);
        return false;
      }
    } catch (error) {
      console.error("❌ Failed to check JWT persistence:", error);
      return false;
    }
  },

  /**
   * Run all tests
   */
  async runAllTests() {
    console.log("🧪 Running Turnkey session persistence tests...\n");
    
    console.log("1. Checking JWT token persistence:");
    await this.checkJWTPersistence();
    
    console.log("\n2. Checking Turnkey session data persistence:");
    await this.checkSessionPersistence();
    
    console.log("\n✅ Tests completed. Check the logs above for results.");
  },

  /**
   * Simulate page refresh test
   */
  async simulatePageRefresh() {
    console.log("🔄 Simulating page refresh test...");
    
    // Check session before "refresh"
    console.log("Before refresh:");
    const beforeJWT = await this.checkJWTPersistence();
    const beforeSession = await this.checkSessionPersistence();
    
    // Simulate a small delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Check session after "refresh"
    console.log("\nAfter refresh simulation:");
    const afterJWT = await this.checkJWTPersistence();
    const afterSession = await this.checkSessionPersistence();
    
    console.log("\n📊 Results:");
    console.log(`JWT persistence: ${beforeJWT === afterJWT ? "✅ PASS" : "❌ FAIL"}`);
    console.log(`Session persistence: ${!!beforeSession === !!afterSession ? "✅ PASS" : "❌ FAIL"}`);
  }
};

// Make it available globally for browser console testing
if (typeof window !== "undefined") {
  (window as any).testTurnkeySession = testTurnkeySessionPersistence;
}
